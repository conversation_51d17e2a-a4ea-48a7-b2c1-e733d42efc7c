@import "tailwindcss";
@import "tw-animate-css";



@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Modern Brand Color System */
  --color-backgroundPrimary: var(--background-primary);
  --color-backgroundSecondary: var(--background-secondary);
  --color-backgroundTertiary: var(--background-tertiary);
  --color-textPrimary: var(--text-primary);
  --color-textSecondary: var(--text-secondary);
  --color-textMuted: var(--text-muted);
  --color-brandPrimary: var(--brand-primary);
  --color-brandSecondary: var(--brand-secondary);
  --color-brandAccent: var(--brand-accent);
  --color-brandGradientStart: var(--brand-gradient-start);
  --color-brandGradientEnd: var(--brand-gradient-end);
  --color-borderPrimary: var(--border-primary);
  --color-borderSecondary: var(--border-secondary);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-glassBackground: var(--glass-background);
  --color-shadowPrimary: var(--shadow-primary);
  --color-shadowSecondary: var(--shadow-secondary);
}

:root {
  --radius: 0.75rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.396 0.115 266.89);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.396 0.115 266.89);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.396 0.115 266.89);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.396 0.115 266.89);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.396 0.115 266.89);

  /* Modern Color System - Clean Professional Palette */
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-tertiary: #f1f5f9;
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;

  /* Clean Brand Colors - Accent & Violet Theme */
  --brand-primary: #6366f1;        /* Indigo - Primary brand color */
  --brand-secondary: #8b5cf6;      /* Violet - Secondary brand color */
  --brand-accent: #10b981;         /* Emerald - Accent for success/highlights */
  --brand-neutral: #64748b;        /* Slate - Neutral actions */

  /* Semantic Colors */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Glass & Shadow Effects */
  --glass-background: rgba(255, 255, 255, 0.8);
  --shadow-primary: rgba(15, 23, 42, 0.04);
  --shadow-secondary: rgba(15, 23, 42, 0.08);
  --shadow-accent: rgba(99, 102, 241, 0.15);
}



@layer base {
  * {
    @apply border-borderPrimary outline-ring/50;
  }

  /* Remove default outline from links */
  a {
    outline: none;
  }

  /* Custom focus styles for better UX */
  a:focus-visible {
    outline: 2px solid var(--brand-primary);
    outline-offset: 2px;
    border-radius: 4px;
  }

  body {
    @apply bg-backgroundPrimary text-textPrimary;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .glass-effect {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--glass-background);
    border: 1px solid var(--border-primary);
  }

  .gradient-text {
    background: linear-gradient(
      135deg,
      var(--brand-primary),
      var(--brand-secondary)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-border {
    background:
      linear-gradient(var(--background-primary), var(--background-primary))
        padding-box,
      linear-gradient(
          135deg,
          var(--brand-primary),
          var(--brand-secondary)
        )
        border-box;
    border: 2px solid transparent;
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  .animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-float-slow {
    animation: floatSlow 8s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: floatDelayed 10s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulseSlow 6s ease-in-out infinite;
  }

  .animate-grid-float {
    animation: gridFloat 20s linear infinite;
  }

  .animate-float-particle-1 {
    animation: floatParticle1 15s ease-in-out infinite;
  }

  .animate-float-particle-2 {
    animation: floatParticle2 12s ease-in-out infinite;
  }

  .animate-float-particle-3 {
    animation: floatParticle3 18s ease-in-out infinite;
  }

  .bg-grid-pattern {
    background-image: radial-gradient(
      circle at 1px 1px,
      rgba(99, 102, 241, 0.1) 1px,
      transparent 0
    );
    background-size: 40px 40px;
  }

  /* Portfolio Theme Container - Isolate theme content within canvas */
  .portfolio-theme-container {
    position: relative;
    isolation: isolate;
    contain: layout style;
    overflow: hidden;
  }

  /* Override theme fixed/sticky positioning to be relative to canvas */
  .portfolio-theme-container [style*="position: fixed"],
  .portfolio-theme-container [style*="position:fixed"],
  .portfolio-theme-container .fixed {
    position: absolute !important;
  }

  .portfolio-theme-container [style*="position: sticky"],
  .portfolio-theme-container [style*="position:sticky"],
  .portfolio-theme-container .sticky {
    position: relative !important;
  }

  /* Ensure theme navbars and headers stay within canvas */
  .portfolio-theme-container nav,
  .portfolio-theme-container header {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    z-index: auto !important;
  }

  /* Generic theme navbar override for editor canvas */
  .portfolio-theme-container .theme-navbar {
    position: relative !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    z-index: auto !important;
  }

  /* Prevent theme content from escaping canvas bounds */
  .portfolio-theme-container * {
    max-width: 100%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes floatSlow {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(1deg);
  }
  50% {
    transform: translateY(-10px) translateX(-10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-30px) translateX(5px) rotate(0.5deg);
  }
}

@keyframes floatDelayed {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  33% {
    transform: translateY(15px) translateX(-15px) rotate(-1deg);
  }
  66% {
    transform: translateY(-25px) translateX(10px) rotate(1deg);
  }
}

@keyframes pulseSlow {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes gridFloat {
  0% {
    transform: translateX(0) translateY(0);
  }
  100% {
    transform: translateX(-40px) translateY(-40px);
  }
}

@keyframes floatParticle1 {
  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-100px) translateX(50px) scale(1.2);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-200px) translateX(-30px) scale(0.8);
    opacity: 0.4;
  }
  75% {
    transform: translateY(-150px) translateX(80px) scale(1.1);
    opacity: 0.5;
  }
}

@keyframes floatParticle2 {
  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.4;
  }
  33% {
    transform: translateY(80px) translateX(-60px) scale(1.3);
    opacity: 0.7;
  }
  66% {
    transform: translateY(120px) translateX(40px) scale(0.9);
    opacity: 0.3;
  }
}

@keyframes floatParticle3 {
  0%,
  100% {
    transform: translateY(0) translateX(0) scale(1);
    opacity: 0.5;
  }
  40% {
    transform: translateY(-80px) translateX(-100px) scale(1.4);
    opacity: 0.8;
  }
  80% {
    transform: translateY(-160px) translateX(60px) scale(0.7);
    opacity: 0.2;
  }
}

/* Smooth transitions for theme switching */
* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--brand-primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--brand-secondary);
}

/* Ensure proper spacing for fixed navbar */
body {
  scroll-padding-top: 80px;
}
