// User Management Types
export type UserPlan = 'free' | 'premium';

export interface User {
  uid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  plan: UserPlan;
  createdAt: Date;
  updatedAt: Date;
  // Premium features
  premiumExpiresAt?: Date;
  // Settings
  settings: {
    emailNotifications: boolean;
    publicProfile: boolean;
  };
}

// Portfolio Data Types
export interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

export interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string;
  description?: string;
  location?: string;
  companyUrl?: string;
}

export interface Skill {
  id: string;
  name: string;
  category?: 'web-development' | 'mobile-development' | 'design' | 'data-science' | 'devops' | 'marketing' | 'business' | 'other';
}

export interface SocialLinks {
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
}




export interface PortfolioData {
  id: string; // Portfolio document ID
  userId: string; // Reference to user
  isPublished: boolean;
  slug: string;
  templateId: string;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  qualifications?: string;
  qualification1?: string;
  qualification2?: string;
  qualification3?: string;
  qualification4?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  contactEmail: string;
  email?: string;
  phone?: string;
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}

export const defaultPortfolioData = (portfolioId: string, userId: string, user: User): PortfolioData => ({
  id: portfolioId,
  userId,
  isPublished: false,
  slug: `${user.displayName?.toLowerCase().replace(/\s+/g, '-')}`,
  templateId: 'creative-theme-v1',
  userName: user.displayName || '',
  profession: '',
  about: '',
  bio: '',
  contactEmail: user.email,
  email: user.email,
  profileImageUrl: '',
  resumeUrl: '',
  projects: [
    {
      id: 'default-project',
      title: '',
      description: '',
      url: '',
      liveUrl: '',
      imageUrl: ''
    }
  ],
  experiences: [
    {
      id: 'default-experience',
      role: '',
      company: '',
      duration: '',
      description: '',
      location: ''
    }
  ],
  skills: [
    {
      id: 'default-skill-1',
      name: '',
      category: 'web-development'
    },
    {
      id: 'default-skill-2',
      name: '',
      category: 'web-development'
    },
    {
      id: 'default-skill-3',
      name: '',
      category: 'web-development'
    }
  ],
  githubUrl: '',
  linkedinUrl: '',
  twitterUrl: '',
  qualification1: "",
  qualification2: "",
  createdAt: new Date(),
  updatedAt: new Date(),
});


// Helper functions for user management
export const createDefaultUser = (firebaseUser: { uid: string; email: string; displayName?: string | null; photoURL?: string | null }): User => ({
  uid: firebaseUser.uid,
  email: firebaseUser.email,
  displayName: firebaseUser.displayName || firebaseUser.email.split('@')[0],
  photoURL: firebaseUser.photoURL || null,
  plan: 'free',
  createdAt: new Date(),
  updatedAt: new Date(),
  settings: {
    emailNotifications: true,
    publicProfile: true,
  },
});

// Check if user has premium features
export const hasPremiumAccess = (user: User): boolean => {
  if (user.plan !== 'premium') return false;
  if (!user.premiumExpiresAt) return false;
  return new Date() < user.premiumExpiresAt;
};

// Premium feature checks
export const canExportPortfolio = (user: User): boolean => hasPremiumAccess(user);
export const canUsePremiumThemes = (user: User): boolean => hasPremiumAccess(user);
export const getMaxPortfolios = (user: User): number => hasPremiumAccess(user) ? 10 : 1;

// A type for upload mutations, makes passing it easier
export type UploadFunction = (vars: { file: File; type: 'profile' | 'resume' | 'project'; id?: string }) => void;

export interface ProfolifyThemeProps {
  isEditing: boolean;
  serverData?: PortfolioData; // For public, server-rendered pages
  onImageUpload?: UploadFunction; // For editor page
}

// Props for each section component
export interface SectionProps {
  isEditing: boolean;
  serverData?: PortfolioData;
  onImageUpload?: UploadFunction;
}
