import { PortfolioData, Project, Experience } from './types';

export interface ValidationError {
  field: string;
  message: string;
  section?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Phone number validation
export const validatePhoneNumber = (phone: string): boolean => {
  if (!phone || phone.trim() === '') return true; // Optional field
  
  // Remove all non-digit characters for validation
  const digitsOnly = phone.replace(/\D/g, '');
  
  // Must have 10-15 digits (international format)
  if (digitsOnly.length < 10 || digitsOnly.length > 15) {
    return false;
  }
  
  // Basic format validation - should contain mostly digits, spaces, dashes, parentheses, plus
  const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,20}$/;
  return phoneRegex.test(phone.trim());
};

// Email validation
export const validateEmail = (email: string): boolean => {
  if (!email || email.trim() === '') return false; // Required field
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
};

// URL validation
export const validateUrl = (url: string): boolean => {
  if (!url || url.trim() === '') return true; // Optional field
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Validate required text fields
export const validateRequiredText = (text: string | undefined, minLength: number = 1): boolean => {
  return !!(text && text.trim().length >= minLength);
};

// Validate project
export const validateProject = (project: Project, index: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  const section = `Project ${index + 1}`;
  
  if (!validateRequiredText(project.title)) {
    errors.push({
      field: `projects[${index}].title`,
      message: 'Project title is required',
      section
    });
  }
  
  if (!validateRequiredText(project.description)) {
    errors.push({
      field: `projects[${index}].description`,
      message: 'Project description is required',
      section
    });
  }

  // Image validation - required for projects with content
  if (!project.imageUrl || project.imageUrl.trim() === '') {
    errors.push({
      field: `projects[${index}].imageUrl`,
      message: 'Project image is required',
      section
    });
  }

  // URLs are optional, but if provided must be valid
  if (project.url && !validateUrl(project.url)) {
    errors.push({
      field: `projects[${index}].url`,
      message: 'Invalid project URL format',
      section
    });
  }
  
  if (project.liveUrl && !validateUrl(project.liveUrl)) {
    errors.push({
      field: `projects[${index}].liveUrl`,
      message: 'Invalid live URL format',
      section
    });
  }
  
  return errors;
};

// Validate experience
export const validateExperience = (experience: Experience, index: number): ValidationError[] => {
  const errors: ValidationError[] = [];
  const section = `Experience ${index + 1}`;
  
  if (!validateRequiredText(experience.role)) {
    errors.push({
      field: `experiences[${index}].role`,
      message: 'Job title/role is required',
      section
    });
  }
  
  if (!validateRequiredText(experience.company)) {
    errors.push({
      field: `experiences[${index}].company`,
      message: 'Company name is required',
      section
    });
  }
  
  if (!validateRequiredText(experience.duration)) {
    errors.push({
      field: `experiences[${index}].duration`,
      message: 'Duration is required',
      section
    });
  }

  if (!validateRequiredText(experience.location)) {
    errors.push({
      field: `experiences[${index}].location`,
      message: 'Location is required',
      section
    });
  }

  if (!validateRequiredText(experience.description)) {
    errors.push({
      field: `experiences[${index}].description`,
      message: 'Description is required',
      section
    });
  }

  // Company URL is optional, but if provided must be valid
  if (experience.companyUrl && !validateUrl(experience.companyUrl)) {
    errors.push({
      field: `experiences[${index}].companyUrl`,
      message: 'Invalid company URL format',
      section
    });
  }
  
  return errors;
};

// Main portfolio validation function
export const validatePortfolio = (data: PortfolioData): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  console.log('🔍 Validating portfolio data:', {
    userName: data.userName,
    profession: data.profession,
    profileImageUrl: data.profileImageUrl,
    projectsCount: data.projects?.length,
    experiencesCount: data.experiences?.length,
    skillsCount: data.skills?.length,
    projects: data.projects?.map(p => ({
      title: p.title,
      description: p.description,
      imageUrl: p.imageUrl,
      isComplete: !!(p.title && p.description && p.imageUrl)
    })),
    experiences: data.experiences?.map(e => ({
      role: e.role,
      company: e.company,
      duration: e.duration,
      location: e.location,
      description: e.description,
      isComplete: !!(e.role && e.company && e.duration && e.location && e.description)
    })),
    skills: data.skills?.map(s => ({
      name: s.name,
      isEmpty: !s.name
    }))
  });
  
  // Basic required fields
  if (!validateRequiredText(data.userName)) {
    errors.push({
      field: 'userName',
      message: 'Your name is required',
      section: 'Basic Info'
    });
  }
  
  if (!validateRequiredText(data.profession)) {
    errors.push({
      field: 'profession',
      message: 'Your profession/title is required',
      section: 'Basic Info'
    });
  }
  
  // Email validation (required)
  if (!validateEmail(data.email || '')) {
    errors.push({
      field: 'email',
      message: 'A valid email address is required',
      section: 'Contact'
    });
  }
  
  // Phone validation (optional, but must be valid if provided)
  if (data.phone && !validatePhoneNumber(data.phone)) {
    errors.push({
      field: 'phone',
      message: 'Please enter a valid phone number (10-15 digits)',
      section: 'Contact'
    });
  }
  
  // Social links validation (optional, but must be valid URLs if provided)
  if (data.githubUrl && !validateUrl(data.githubUrl)) {
    errors.push({
      field: 'githubUrl',
      message: 'Please enter a valid GitHub URL',
      section: 'Social Links'
    });
  }
  
  if (data.linkedinUrl && !validateUrl(data.linkedinUrl)) {
    errors.push({
      field: 'linkedinUrl',
      message: 'Please enter a valid LinkedIn URL',
      section: 'Social Links'
    });
  }
  
  if (data.twitterUrl && !validateUrl(data.twitterUrl)) {
    errors.push({
      field: 'twitterUrl',
      message: 'Please enter a valid Twitter URL',
      section: 'Social Links'
    });
  }
  
  // Projects validation - validate ALL project cards
  if (!data.projects || data.projects.length === 0) {
    errors.push({
      field: 'projects',
      message: 'At least one project is required',
      section: 'Projects'
    });
  } else {
    // Validate ALL projects (including empty ones)
    data.projects.forEach((project, index) => {
      const projectErrors = validateProject(project, index);
      errors.push(...projectErrors);
    });
  }
  
  // Experience validation - validate ALL experience cards
  if (!data.experiences || data.experiences.length === 0) {
    errors.push({
      field: 'experiences',
      message: 'At least one work experience is required',
      section: 'Experience'
    });
  } else {
    // Validate ALL experiences (including empty ones)
    data.experiences.forEach((experience, index) => {
      const experienceErrors = validateExperience(experience, index);
      errors.push(...experienceErrors);
    });
  }
  
  // Skills validation - at least 3 skills required
  if (!data.skills || data.skills.length === 0) {
    errors.push({
      field: 'skills',
      message: 'At least 3 skills are required',
      section: 'Skills'
    });
  } else {
    // Filter out empty skills
    const validSkills = data.skills.filter(skill => validateRequiredText(skill.name));

    if (validSkills.length < 3) {
      errors.push({
        field: 'skills',
        message: `At least 3 skills are required (currently have ${validSkills.length})`,
        section: 'Skills'
      });
    }
  }
  
  // Hero image validation - required
  if (!data.profileImageUrl || data.profileImageUrl.trim() === '') {
    errors.push({
      field: 'profileImageUrl',
      message: 'Profile image is required',
      section: 'Hero'
    });
  }

  // Bio/About validation - recommended
  if (!validateRequiredText(data.bio)) {
    warnings.push({
      field: 'bio',
      message: 'Adding an about section will help visitors learn more about you',
      section: 'About'
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// Helper function to get validation errors for a specific field
export const getFieldErrors = (validationResult: ValidationResult, fieldName: string): ValidationError[] => {
  return validationResult.errors.filter(error => error.field === fieldName);
};

// Helper function to check if a specific section has errors
export const sectionHasErrors = (validationResult: ValidationResult, sectionName: string): boolean => {
  return validationResult.errors.some(error => error.section === sectionName);
};
