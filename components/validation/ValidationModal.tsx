"use client";
import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ValidationErrorDisplay } from './ValidationErrorDisplay';
import { ValidationResult } from '@/lib/portfolio-validation';
import { AlertCircle, CheckCircle } from 'lucide-react';

interface ValidationModalProps {
  isOpen: boolean;
  onClose: () => void;
  validationResult: ValidationResult;
  onProceedAnyway?: () => void;
  allowProceedWithWarnings?: boolean;
}

export function ValidationModal({ 
  isOpen, 
  onClose, 
  validationResult, 
  onProceedAnyway,
  allowProceedWithWarnings = false 
}: ValidationModalProps) {
  const hasErrors = validationResult.errors.length > 0;
  const hasWarnings = validationResult.warnings.length > 0;
  const hasOnlyWarnings = !hasErrors && hasWarnings;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            {hasErrors ? (
              <>
                <AlertCircle className="h-5 w-5 text-red-500" />
                <span>Portfolio Validation Issues</span>
              </>
            ) : hasOnlyWarnings ? (
              <>
                <CheckCircle className="h-5 w-5 text-yellow-500" />
                <span>Portfolio Ready with Recommendations</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span>Portfolio Validation Passed</span>
              </>
            )}
          </DialogTitle>
          <DialogDescription>
            {hasErrors ? (
              "Please fix the following issues before publishing your portfolio."
            ) : hasOnlyWarnings ? (
              "Your portfolio is ready to publish! Consider these recommendations to make it even better."
            ) : (
              "Your portfolio looks great and is ready to be published!"
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <ValidationErrorDisplay
            errors={validationResult.errors}
            warnings={validationResult.warnings}
            showWarnings={true}
          />

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
            
            {hasOnlyWarnings && allowProceedWithWarnings && onProceedAnyway && (
              <Button
                onClick={onProceedAnyway}
                className="bg-brandAccent hover:bg-brandAccent/90 text-white"
              >
                Publish Anyway
              </Button>
            )}

            {!hasErrors && !hasWarnings && (
              <Button
                onClick={onClose}
                className="bg-brandAccent hover:bg-brandAccent/90 text-white"
              >
                Continue
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
