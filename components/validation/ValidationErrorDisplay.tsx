"use client";
import React from 'react';
import { Alert<PERSON><PERSON>cle, AlertTriangle, X } from 'lucide-react';
import { ValidationError } from '@/lib/portfolio-validation';
import { cn } from '@/lib/utils';

interface ValidationErrorDisplayProps {
  errors: ValidationError[];
  warnings?: ValidationError[];
  className?: string;
  showWarnings?: boolean;
  onDismiss?: () => void;
}

export function ValidationErrorDisplay({ 
  errors, 
  warnings = [], 
  className,
  showWarnings = true,
  onDismiss 
}: ValidationErrorDisplayProps) {
  if (errors.length === 0 && (!showWarnings || warnings.length === 0)) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Errors */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-red-500 mb-2">
                  Please fix the following issues before publishing:
                </h3>
                <div className="space-y-2">
                  {errors.map((error, index) => (
                    <div key={index} className="text-sm text-red-500">
                      {error.section && (
                        <span className="font-medium text-red-500">
                          {error.section}: 
                        </span>
                      )}{' '}
                      {error.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-red-400 hover:text-red-500 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      )}

      {/* Warnings */}
      {showWarnings && warnings.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-yellow-800 mb-2">
                  Recommendations to improve your portfolio:
                </h3>
                <div className="space-y-2">
                  {warnings.map((warning, index) => (
                    <div key={index} className="text-sm text-yellow-700">
                      {warning.section && (
                        <span className="font-medium text-yellow-800">
                          {warning.section}: 
                        </span>
                      )}{' '}
                      {warning.message}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            {onDismiss && (
              <button
                onClick={onDismiss}
                className="text-yellow-400 hover:text-yellow-600 transition-colors"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Inline field error component
interface FieldErrorProps {
  error?: string;
  className?: string;
}

export function FieldError({ error, className }: FieldErrorProps) {
  if (!error) return null;

  return (
    <div className={cn("flex items-center space-x-1 mt-1", className)}>
      <AlertCircle className="h-3 w-3 text-red-400 flex-shrink-0" />
      <span className="text-xs text-red-500">{error}</span>
    </div>
  );
}

// Field wrapper with validation styling
interface ValidatedFieldProps {
  children: React.ReactNode;
  error?: string;
  warning?: string;
  className?: string;
}

export function ValidatedField({ children, error, warning, className }: ValidatedFieldProps) {
  return (
    <div className={cn("relative", className)}>
      <div className={cn(
        "transition-all duration-200",
        error && "ring-2 ring-red-200 rounded-md",
        warning && !error && "ring-2 ring-yellow-200 rounded-md"
      )}>
        {children}
      </div>
      {error && <FieldError error={error} />}
      {warning && !error && (
        <div className="flex items-center space-x-1 mt-1">
          <AlertTriangle className="h-3 w-3 text-yellow-500 flex-shrink-0" />
          <span className="text-xs text-yellow-600">{warning}</span>
        </div>
      )}
    </div>
  );
}
